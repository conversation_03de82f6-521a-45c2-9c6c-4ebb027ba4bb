import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/cupertino.dart';
import 'package:simple_todo_app/chat_page.dart';
import 'header.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'iOS Style App',
      theme: ThemeData(
        platform: TargetPlatform.iOS,
        primarySwatch: Colors.blue,
        appBarTheme: const AppBarTheme(
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        ),
      ),
      home: const HomePage(),
      onGenerateRoute: (settings) {
        // iOS style page route
        return CupertinoPageRoute(
          builder: (context) {
            switch (settings.name) {
              case '/':
                return const HomePage();
              case '/chat':
                return const ChatPage();
              default:
                return const HomePage();
            }
          },
        );
      },
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            const Header(
              title: 'Home',
              allowBack: false,
            ),
            Expanded(
              child: Center(
                child: CupertinoButton(
                  child: const Text('Go to Second Page'),
                  onPressed: () {
                    Navigator.pushNamed(context, '/chat');
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SecondPage extends StatelessWidget {
  const SecondPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            const Header(
              title: 'Second Page',
              allowBack: true,
            ),
            Expanded(
              child: Container(
                color: Colors.white,
                child: const Center(
                  child: Text('This is the second page'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
