class AIModel {
  final String id;
  final String name;
  final String provider;
  final String apiModel;
  final int maxTokens;

  const AIModel({
    required this.id,
    required this.name,
    required this.provider,
    required this.apiModel,
    required this.maxTokens,
  });
}

class AIModels {
  static const List<AIModel> models = [
    // OpenAI Reasoning Models
    AIModel(
      id: 'gpt-4.1',
      name: 'GPT-4.1 (Reasoning)',
      provider: 'openai',
      apiModel: 'gpt-4.1',
      maxTokens: 128000,
    ),
    AIModel(
      id: 'o1-preview',
      name: 'O1 Preview',
      provider: 'openai',
      apiModel: 'o1-preview',
      maxTokens: 128000,
    ),
    AIModel(
      id: 'o1-mini',
      name: 'O1 Mini',
      provider: 'openai',
      apiModel: 'o1-mini',
      maxTokens: 128000,
    ),

    // Standard OpenAI Models
    AIModel(
      id: 'gpt-4-turbo',
      name: 'GPT-4 Turbo',
      provider: 'openai',
      apiModel: 'gpt-4-turbo-preview',
      maxTokens: 128000,
    ),
    AIModel(
      id: 'gpt-4',
      name: 'GPT-4',
      provider: 'openai',
      apiModel: 'gpt-4',
      maxTokens: 8192,
    ),
    AIModel(
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      provider: 'openai',
      apiModel: 'gpt-3.5-turbo',
      maxTokens: 16384,
    ),

    // Claude Models
    AIModel(
      id: 'claude-3-opus',
      name: 'Claude 3 Opus',
      provider: 'anthropic',
      apiModel: 'claude-3-opus-20240229',
      maxTokens: 200000,
    ),
    AIModel(
      id: 'claude-3-sonnet',
      name: 'Claude 3 Sonnet',
      provider: 'anthropic',
      apiModel: 'claude-3-sonnet-20240229',
      maxTokens: 200000,
    ),
    AIModel(
      id: 'claude-3-haiku',
      name: 'Claude 3 Haiku',
      provider: 'anthropic',
      apiModel: 'claude-3-haiku-20240307',
      maxTokens: 200000,
    ),
  ];

  static AIModel getDefaultModel() => models.first;

  static AIModel? getModelById(String id) {
    try {
      return models.firstWhere((model) => model.id == id);
    } catch (e) {
      return null;
    }
  }
}
