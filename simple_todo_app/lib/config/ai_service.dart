import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'models.dart';
import 'api_config.dart';

class AIService {
  static Future<String> sendMessage({
    required String message,
    required AIModel model,
    List<Map<String, dynamic>>? conversationHistory,
    File? attachedFile,
  }) async {
    try {
      if (model.provider == 'openai') {
        return await _sendOpenAIMessage(message, model, conversationHistory);
      } else if (model.provider == 'anthropic') {
        return await _sendClaudeMessage(message, model, conversationHistory);
      } else {
        throw Exception('Unknown provider: ${model.provider}');
      }
    } catch (e) {
      throw Exception('Failed to get AI response: $e');
    }
  }

  // Standard chat completions format for all OpenAI models
  static Future<String> _sendOpenAIMessage(
    String message,
    AIModel model,
    List<Map<String, dynamic>>? history,
  ) async {
    final messages = [
      if (history != null) ...history,
      {'role': 'user', 'content': message},
    ];

    final response = await http.post(
      Uri.parse('<https://api.openai.com/v1/chat/completions>'), // Fixed URL
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${APIConfig.openAIKey}',
      },
      body: jsonEncode({
        'model': model.apiModel,
        'messages': messages,
        'max_tokens': 1000,
        'temperature': 0.7,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['choices'][0]['message']['content'];
    } else {
      throw Exception('OpenAI API error: ${response.body}');
    }
  }

  static Future<String> _sendClaudeMessage(
    String message,
    AIModel model,
    List<Map<String, dynamic>>? history,
  ) async {
    final messages = [
      if (history != null)
        ...history.map((msg) => {
              'role': msg['role'] == 'user' ? 'user' : 'assistant',
              'content': msg['content'],
            }),
      {'role': 'user', 'content': message},
    ];

    final response = await http.post(
      Uri.parse('<https://api.anthropic.com/v1/messages>'), // Fixed URL
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': APIConfig.anthropicKey,
        'anthropic-version': '2023-06-01',
      },
      body: jsonEncode({
        'model': model.apiModel,
        'messages': messages,
        'max_tokens': 1000,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['content'][0]['text'];
    } else {
      throw Exception('Claude API error: ${response.body}');
    }
  }

  static Future<String> transcribeAudio(File audioFile) async {
    final request = http.MultipartRequest(
      'POST',
      Uri.parse(
          '<https://api.openai.com/v1/audio/transcriptions>'), // Fixed URL
    );

    request.headers['Authorization'] = 'Bearer ${APIConfig.openAIKey}';
    request.fields['model'] = 'whisper-1';
    request.files.add(
      await http.MultipartFile.fromPath('file', audioFile.path),
    );

    final response = await request.send();
    final responseData = await response.stream.bytesToString();

    if (response.statusCode == 200) {
      final data = jsonDecode(responseData);
      return data['text'];
    } else {
      throw Exception('Whisper API error: $responseData');
    }
  }
}
