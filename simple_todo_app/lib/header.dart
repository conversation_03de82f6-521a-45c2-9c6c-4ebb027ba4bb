import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

class Header extends StatelessWidget {
  final String title;
  final bool allowBack;

  const Header({
    super.key,
    required this.title,
    required this.allowBack,
  });

  @override
  Widget build(BuildContext context) {
    // Set status bar style to match header
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );

    return Container(
      color: CupertinoColors.systemBackground,
      child: Column(
        children: [
          // Status bar padding
          SizedBox(height: MediaQuery.of(context).padding.top),
          // Header content
          Container(
            height: 44.0, // iOS standard navigation bar height
            decoration: BoxDecoration(
              color: CupertinoColors.systemBackground,
              border: Border(
                bottom: BorderSide(
                  color: CupertinoColors.separator.withOpacity(0.2),
                  width: 0.5,
                ),
              ),
            ),
            child: Stack(
              children: [
                // Back button
                if (allowBack)
                  Positioned(
                    left: 0,
                    child: CupertinoButton(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Row(
                        children: [
                          Icon(
                            CupertinoIcons.back,
                            size: 28,
                            color: CupertinoColors.activeBlue,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Back',
                            style: TextStyle(
                              fontSize: 17,
                              color: CupertinoColors.activeBlue,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                // Title
                Center(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.w600,
                      color: CupertinoColors.label,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
